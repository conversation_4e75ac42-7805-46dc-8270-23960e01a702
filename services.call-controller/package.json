{"name": "cortexa-call-controller", "version": "0.1.0", "description": "Cortexa Call Controller Service", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node -r tsconfig-paths/register dist/index.js", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.14.0", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "kafkajs": "^2.2.4", "prom-client": "^15.1.3", "tsconfig-paths": "^4.2.0", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^4.0.17"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "prisma": "^6.14.0", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "keywords": ["cortexa", "call-controller", "typescript", "express", "microservice"], "author": "<PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=18.0.0"}, "type": "commonjs"}