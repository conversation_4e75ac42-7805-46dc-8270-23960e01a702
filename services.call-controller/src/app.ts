import express from 'express';
import cors from 'cors';
import helmet from 'helmet';

import { config } from '@/config';
import { setupServiceLogging, getLogger } from '@/core/logger';
import { injectAppContext } from '@/api/middleware/context';
import { errorHand<PERSON>, notFoundHandler } from '@/api/middleware/error-handler';
import { metricsMiddleware, metricsHandler, setupMetrics } from '@/core/metrics';
import apiV1Router from '@/api/v1/routes';

const logger = getLogger('App');

/**
 * Create and configure the Express application.
 * @returns Configured Express app
 */
export function createApp(): express.Application {
  const app = express();

  // Setup logging
  setupServiceLogging(config.serviceName, config.debug ? 'debug' : 'info');
  
  // Setup metrics
  setupMetrics();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: false, // Disable CSP for API service
  }));

  // CORS middleware
  app.use(cors({
    origin: '*', // Configure appropriately for production
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Metrics middleware (before routes)
  app.use(metricsMiddleware);

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      service: config.serviceName,
      timestamp: new Date().toISOString(),
    });
  });

  // Metrics endpoint
  app.get('/metrics', metricsHandler);

  // Inject application context into all API routes
  app.use('/api', injectAppContext);

  // API routes
  app.use('/api/v1', apiV1Router);

  // 404 handler for unmatched routes
  app.use(notFoundHandler);

  // Global error handler (must be last)
  app.use(errorHandler);

  logger.info('Express application configured');

  return app;
}
