import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Base service settings schema (similar to Python BaseServiceSettings)
const BaseServiceSettingsSchema = z.object({
  serviceName: z.string().default('call-controller'),
  debug: z.boolean().default(false),
  host: z.string().default('0.0.0.0'),
  port: z.number().int().positive().default(8002),
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
});

// Call Controller specific settings schema
const CallControllerSettingsSchema = BaseServiceSettingsSchema.extend({
  // Database configuration
  databaseUrl: z.url(),
  
  // Kafka configuration
  kafkaBootstrapServers: z.string().default('localhost:9092'),
  
  // Internal service URLs
  voiceRouterUrl: z.url().default('http://localhost:3000'),
});

// Parse and validate environment variables
const parseConfig = () => {
  const rawConfig = {
    serviceName: process.env.SERVICE_NAME,
    debug: process.env.DEBUG === 'true',
    host: process.env.HOST,
    port: process.env.PORT ? parseInt(process.env.PORT, 10) : undefined,
    nodeEnv: process.env.NODE_ENV,
    databaseUrl: process.env.DATABASE_URL,
    kafkaBootstrapServers: process.env.KAFKA_BOOTSTRAP_SERVERS,
    voiceRouterUrl: process.env.VOICE_ROUTER_URL,
  };

  try {
    return CallControllerSettingsSchema.parse(rawConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Configuration validation error:', error.flatten());
      process.exit(1);
    }
    throw error;
  }
};

// Export the validated configuration
export const config = parseConfig();

// Export types
export type CallControllerSettings = z.infer<typeof CallControllerSettingsSchema>;
export type BaseServiceSettings = z.infer<typeof BaseServiceSettingsSchema>;
