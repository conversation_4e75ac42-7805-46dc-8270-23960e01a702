import { Request, Response, NextFunction } from 'express';
import { getAppContext, ApplicationContext } from '@/core/context';
import { getLogger } from '@/core/logger';


// Define extended request interface
interface ExtendedRequest extends Request {
  appContext: ApplicationContext;
}


const logger = getLogger('ContextMiddleware');

/**
 * Middleware to inject the application context into each request.
 * Similar to FastAPI's Depends() pattern.
 */
export async function injectAppContext(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const appContext = await getAppContext();
    
    if (!appContext.isInitialized) {
      logger.error('Application context not initialized');
      (res as Response).status(503).json({
        error: 'Service Unavailable',
        message: 'Application context not initialized',
      });
      return;
    }

    // Inject the context into the request
    (req as ExtendedRequest).appContext = appContext;
    next();

  } catch (error) {
    logger.error('Failed to inject application context:', error);
    (res as Response).status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to initialize request context',
    });
  }
}
