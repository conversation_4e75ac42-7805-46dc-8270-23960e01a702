import { Request, Response, NextFunction } from 'express';
import z, { ZodError } from 'zod';
import { getLogger } from '@/core/logger';

const logger = getLogger('ErrorHandler');

/**
 * Global error handler middleware for Express.
 * Handles different types of errors and returns appropriate responses.
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
  });

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid request data',
      details: z.treeifyError(error),
    });
    return;
  }

  // Handle known application errors
  if (error.message.includes('not found')) {
    res.status(404).json({
      error: 'Not Found',
      message: error.message,
    });
    return;
  }

  if (error.message.includes('Voice-router error')) {
    res.status(502).json({
      error: 'Bad Gateway',
      message: error.message,
    });
    return;
  }

  // Handle database connection errors
  if (error.message.includes('database') || error.message.includes('connection')) {
    res.status(503).json({
      error: 'Service Unavailable',
      message: 'Database connection error',
    });
    return;
  }

  // Default to 500 Internal Server Error
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
  });
}

/**
 * 404 handler for unmatched routes.
 */
export function notFoundHandler(req: Request, res: Response): void {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`,
  });
}
