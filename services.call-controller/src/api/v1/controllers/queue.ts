import { Request, Response } from 'express';
import { CallStatus } from '@/models/call';
import { getLogger } from '@/core/logger';
import '@/types/express'; // Import type definitions to extend Express Request interface

const logger = getLogger('QueueController');

/**
 * Get queue status information.
 * GET /api/v1/queue/status
 */
export async function getQueueStatus(req: Request, res: Response): Promise<void> {
  try {
    logger.info('Getting queue status');

    // Get call counts by status
    const queuedCount = await req.appContext.callServiceInstance.getCallCount(CallStatus.QUEUED);
    const activeCount = await req.appContext.callServiceInstance.getCallCount(CallStatus.ACTIVE);
    const totalCount = await req.appContext.callServiceInstance.getCallCount();

    // Calculate queue metrics
    const response = {
      queue: {
        waiting: queuedCount,
        active: activeCount,
        total: totalCount,
      },
      operators: {
        // TODO: Implement operator status tracking
        available: 0,
        busy: 0,
        offline: 0,
        total: 0,
      },
      metrics: {
        averageWaitTime: 0, // TODO: Calculate from call data
        averageCallDuration: 0, // TODO: Calculate from completed calls
        timestamp: new Date().toISOString(),
      },
    };

    res.json(response);

  } catch (error) {
    logger.error('Failed to get queue status:', error);
    throw error;
  }
}
