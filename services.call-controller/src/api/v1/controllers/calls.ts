import { Request, Response } from 'express';
import { CreateCallRequestSchema, toCallResponse } from '@/schemas/call';
import { getLogger } from '@/core/logger';
import '@/types/express'; // Import type definitions to extend Express Request interface

const logger = getLogger('CallsController');

/**
 * Create a new call.
 * POST /api/v1/calls
 */
export async function createCall(req: Request, res: Response): Promise<void> {
  try {
    // Validate request body (currently empty but ready for future fields)
    CreateCallRequestSchema.parse(req.body);

    logger.info('Creating new call via API');

    // Create the call using the service
    const newCall = await req.appContext.callServiceInstance.createNewCall();

    // Convert to API response format
    const response = toCallResponse(newCall);

    logger.info(`Call created successfully: ${newCall.id}`);

    res.status(201).json(response);

  } catch (error) {
    logger.error('Failed to create call:', error);
    
    // Let the error handler middleware handle the error
    throw error;
  }
}

/**
 * End a call.
 * POST /api/v1/calls/{callId}/end
 */
export async function endCall(req: Request, res: Response): Promise<void> {
  try {
    const { callId } = req.params;

    if (!callId) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Call ID is required',
      });
      return;
    }

    logger.info(`Ending call via API: ${callId}`);

    // End the call using the service
    const updatedCall = await req.appContext.callServiceInstance.endCall(callId);

    if (!updatedCall) {
      res.status(404).json({
        error: 'Not Found',
        message: 'Call not found',
      });
      return;
    }

    logger.info(`Call ended successfully: ${callId}`);

    // Return 204 No Content for successful deletion
    res.status(204).send();

  } catch (error) {
    logger.error(`Failed to end call ${req.params.callId}:`, error);
    
    // Let the error handler middleware handle the error
    throw error;
  }
}

/**
 * Get a call by ID.
 * GET /api/v1/calls/{callId}
 */
export async function getCall(req: Request, res: Response): Promise<void> {
  try {
    const { callId } = req.params;

    if (!callId) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Call ID is required',
      });
      return;
    }

    const call = await req.appContext.callServiceInstance.getCall(callId);

    if (!call) {
      res.status(404).json({
        error: 'Not Found',
        message: 'Call not found',
      });
      return;
    }

    const response = toCallResponse(call);
    res.json(response);

  } catch (error) {
    logger.error(`Failed to get call ${req.params.callId}:`, error);
    throw error;
  }
}

/**
 * Get all calls.
 * GET /api/v1/calls
 */
export async function getCalls(req: Request, res: Response): Promise<void> {
  try {
    const calls = await req.appContext.callServiceInstance.getCalls();
    const response = calls.map(toCallResponse);
    
    res.json(response);

  } catch (error) {
    logger.error('Failed to get calls:', error);
    throw error;
  }
}
