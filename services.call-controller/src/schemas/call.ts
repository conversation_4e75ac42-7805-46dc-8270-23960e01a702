import { z } from 'zod';
import { CallStatus } from '@/models/call';

// Base call schema
export const CallBaseSchema = z.object({
  // Base properties can be added here if needed
});

// Call response schema (for API responses)
export const CallResponseSchema = z.object({
  id: z.uuid(),
  status: z.enum(CallStatus),
  createdAt: z.date(),
  endedAt: z.date().nullable(),
  mediaSessionId: z.string().nullable(),
  translationForkId: z.string().nullable(),
});

export type CallResponse = z.infer<typeof CallResponseSchema>;

// Call creation request schema
export const CreateCallRequestSchema = z.object({
  // Currently no required fields for call creation
  // Future fields like priority, caller info, etc. can be added here
});

export type CreateCallRequest = z.infer<typeof CreateCallRequestSchema>;

// Call update request schema
export const UpdateCallRequestSchema = z.object({
  status: z.enum(CallStatus).optional(),
});

export type UpdateCallRequest = z.infer<typeof UpdateCallRequestSchema>;

// Helper function to convert database Call to API response
export function toCallResponse(call: {
  id: string;
  status: CallStatus;
  createdAt: Date;
  endedAt: Date | null;
  mediaSessionId: string | null;
  translationForkId: string | null;
}): CallResponse {
  return {
    id: call.id,
    status: call.status,
    createdAt: call.createdAt,
    endedAt: call.endedAt,
    mediaSessionId: call.mediaSessionId,
    translationForkId: call.translationForkId,
  };
}
