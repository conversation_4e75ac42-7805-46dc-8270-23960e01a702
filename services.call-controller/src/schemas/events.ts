import { z } from 'zod';

// Base event schema
export const BaseEventSchema = z.object({
  eventId: z.uuid(),
  eventType: z.string(),
  timestamp: z.date(),
  sourceService: z.string().default('call-controller'),
});

export type BaseEvent = z.infer<typeof BaseEventSchema>;

// Call Started Event
export const CallStartedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('call.started'),
  callId: z.uuid(),
});

export type CallStartedEvent = z.infer<typeof CallStartedEventSchema>;

// Call Ended Event
export const CallEndedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('call.ended'),
  callId: z.uuid(),
  durationSeconds: z.number().positive(),
});

export type CallEndedEvent = z.infer<typeof CallEndedEventSchema>;

// Call Muted Event
export const CallMutedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('call.muted'),
  callId: z.uuid(),
});

export type CallMutedEvent = z.infer<typeof CallMutedEventSchema>;

// Call Unmuted Event
export const CallUnmutedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('call.unmuted'),
  callId: z.uuid(),
});

export type CallUnmutedEvent = z.infer<typeof CallUnmutedEventSchema>;

// Union type for all call events
export type CallEvent = CallStartedEvent | CallEndedEvent | CallMutedEvent | CallUnmutedEvent;

// Helper function to create a base event
export function createBaseEvent(eventType: string): Omit<BaseEvent, 'eventType'> {
  return {
    eventId: crypto.randomUUID(),
    timestamp: new Date(),
    sourceService: 'call-controller',
  };
}

// Helper functions to create specific events
export function createCallStartedEvent(callId: string): CallStartedEvent {
  return {
    ...createBaseEvent('call.started'),
    eventType: 'call.started',
    callId,
  };
}

export function createCallEndedEvent(callId: string, durationSeconds: number): CallEndedEvent {
  return {
    ...createBaseEvent('call.ended'),
    eventType: 'call.ended',
    callId,
    durationSeconds,
  };
}

export function createCallMutedEvent(callId: string): CallMutedEvent {
  return {
    ...createBaseEvent('call.muted'),
    eventType: 'call.muted',
    callId,
  };
}

export function createCallUnmutedEvent(callId: string): CallUnmutedEvent {
  return {
    ...createBaseEvent('call.unmuted'),
    eventType: 'call.unmuted',
    callId,
  };
}
