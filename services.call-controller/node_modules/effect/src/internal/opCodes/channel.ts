/** @internal */
export const OP_BRACKET_OUT = "BracketOut" as const

/** @internal */
export type OP_BRACKET_OUT = typeof OP_BRACKET_OUT

/** @internal */
export const OP_BRIDGE = "Bridge" as const

/** @internal */
export type OP_BRIDGE = typeof OP_BRIDGE

/** @internal */
export const OP_CONCAT_ALL = "ConcatAll" as const

/** @internal */
export type OP_CONCAT_ALL = typeof OP_CONCAT_ALL

/** @internal */
export const OP_EMIT = "Emit" as const

/** @internal */
export type OP_EMIT = typeof OP_EMIT

/** @internal */
export const OP_ENSURING = "Ensuring" as const

/** @internal */
export type OP_ENSURING = typeof OP_ENSURING

/** @internal */
export const OP_FAIL = "Fail" as const

/** @internal */
export type OP_FAIL = typeof OP_FAIL

/** @internal */
export const OP_FOLD = "Fold" as const

/** @internal */
export type OP_FOLD = typeof OP_FOLD

/** @internal */
export const OP_FROM_EFFECT = "FromEffect" as const

/** @internal */
export type OP_FROM_EFFECT = typeof OP_FROM_EFFECT

/** @internal */
export const OP_PIPE_TO = "PipeTo" as const

/** @internal */
export type OP_PIPE_TO = typeof OP_PIPE_TO

/** @internal */
export const OP_PROVIDE = "Provide" as const

/** @internal */
export type OP_PROVIDE = typeof OP_PROVIDE

/** @internal */
export const OP_READ = "Read" as const

/** @internal */
export type OP_READ = typeof OP_READ

/** @internal */
export const OP_SUCCEED = "Succeed" as const

/** @internal */
export type OP_SUCCEED = typeof OP_SUCCEED

/** @internal */
export const OP_SUCCEED_NOW = "SucceedNow" as const

/** @internal */
export type OP_SUCCEED_NOW = typeof OP_SUCCEED_NOW

/** @internal */
export const OP_SUSPEND = "Suspend" as const

/** @internal */
export type OP_SUSPEND = typeof OP_SUSPEND
