/** @internal */
export type OP_CONSTANT = typeof OP_CONSTANT

/** @internal */
export const OP_CONSTANT = "Constant" as const

/** @internal */
export type OP_FAIL = typeof OP_FAIL

/** @internal */
export const OP_FAIL = "Fail" as const

/** @internal */
export type OP_FALLBACK = typeof OP_FALLBACK

/** @internal */
export const OP_FALLBACK = "Fallback" as const

/** @internal */
export type OP_DESCRIBED = typeof OP_DESCRIBED

/** @internal */
export const OP_DESCRIBED = "Described" as const

/** @internal */
export type OP_LAZY = typeof OP_LAZY

/** @internal */
export const OP_LAZY = "Lazy" as const

/** @internal */
export type OP_MAP_OR_FAIL = typeof OP_MAP_OR_FAIL

/** @internal */
export const OP_MAP_OR_FAIL = "MapOrFail" as const

/** @internal */
export type OP_NESTED = typeof OP_NESTED

/** @internal */
export const OP_NESTED = "Nested" as const

/** @internal */
export type OP_PRIMITIVE = typeof OP_PRIMITIVE

/** @internal */
export const OP_PRIMITIVE = "Primitive" as const

/** @internal */
export type OP_SEQUENCE = typeof OP_SEQUENCE

/** @internal */
export const OP_SEQUENCE = "Sequence" as const

/** @internal */
export type OP_HASHMAP = typeof OP_HASHMAP

/** @internal */
export const OP_HASHMAP = "HashMap" as const

/** @internal */
export type OP_ZIP_WITH = typeof OP_ZIP_WITH

/** @internal */
export const OP_ZIP_WITH = "ZipWith" as const
